module.exports = {
  apps: [
    {
      name: 'fin_llm_splitter',
      script: 'main.py',
      interpreter: 'python3',
      cwd: '/opt/00_APPS/FINITY_AI/fin_llm_splitter',
      exec_mode: 'fork',
      env_file: '.env',                 // Membaca semua variables dari .env file

      // Base environment variables (akan di-merge dengan .env)
      env: {
        NODE_ENV: 'development',
        PYTHONPATH: '/opt/00_APPS/FINITY_AI/fin_llm_splitter',
        PYTHONUNBUFFERED: '1'
      },

      // Production environment (akan di-merge dengan .env)
      env_production: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/00_APPS/FINITY_AI/fin_llm_splitter',
        PYTHONUNBUFFERED: '1'
      },

      // Process management
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',

      // Restart policy
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',

      // Logging
      out_file: './logs/output.log',
      error_file: './logs/error.log',
      log_file: './logs/combined.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // Health monitoring
      kill_timeout: 5000,
      listen_timeout: 3000,

      // Watch options
      ignore_watch: [
        'node_modules',
        'logs',
        '__pycache__',
        '*.pyc',
        '.git',
        'venv'
      ]
    }
  ],

  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-repo/fin-task-aggregator.git',
      path: '/opt/00_APPS/FINITY_AI/fin_llm_splitter',
      'post-deploy': 'pip install -r requirements.txt && pm2 reload ecosystem.config.js --env production'
    }
  }
};
