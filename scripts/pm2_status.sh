#!/bin/bash

# Define colors
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load .env and get SERVICE_NAME
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo -e "${RED}✖ .env file not found. Status check aborted.${NC}"
    exit 1
fi

# Validate SERVICE_NAME
if [ -z "$SERVICE_NAME" ]; then
    echo -e "${RED}✖ SERVICE_NAME not defined in .env. Status check aborted.${NC}"
    exit 1
fi

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "📊 PM2 Status for: $SERVICE_NAME"
echo -e "═════════════════════════════════════════════${NC}"

# Show PM2 status
echo -e "${YELLOW}➤ Process Status:${NC}"
pm2 status

# Check if process exists before proceeding
if pm2 describe "$SERVICE_NAME" > /dev/null 2>&1; then
    echo -e "\n${YELLOW}➤ Process Information:${NC}"
    pm2 info "$SERVICE_NAME" 2>/dev/null

    echo -e "\n${YELLOW}➤ Memory Usage Snapshot:${NC}"
    pm2 ls | grep "$SERVICE_NAME"

    echo -e "\n${YELLOW}➤ Recent Logs (last 20 lines):${NC}"
    pm2 logs "$SERVICE_NAME" --lines 20 --nostream 2>/dev/null
else
    echo -e "${RED}✖ Process '$SERVICE_NAME' not found in PM2 list.${NC}"
fi

# Help Section
echo -e "\n${BLUE}📘 Common Commands:${NC}"
echo -e "  ${GREEN}pm2 logs $SERVICE_NAME${NC}         - View live logs"
echo -e "  ${GREEN}pm2 restart $SERVICE_NAME${NC}      - Restart service"
echo -e "  ${GREEN}pm2 reload $SERVICE_NAME${NC}       - Reload service (zero downtime)"
echo -e "  ${GREEN}pm2 stop $SERVICE_NAME${NC}         - Stop service"
echo -e "  ${GREEN}pm2 start $SERVICE_NAME${NC}        - Start service"
