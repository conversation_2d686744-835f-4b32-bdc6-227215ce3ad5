#!/bin/bash

# Define colors
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Load .env and extract SERVICE_NAME
if [[ -f .env ]]; then
  export $(grep -v '^#' .env | xargs)
else
  echo -e "${RED}✖ .env file not found. Aborting.${NC}"
  exit 1
fi

# Validate SERVICE_NAME
if [[ -z "$SERVICE_NAME" ]]; then
  echo -e "${RED}✖ SERVICE_NAME is not set in .env file. Aborting.${NC}"
  exit 1
fi

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "💥 Stopping PM2 Service: $SERVICE_NAME"
echo -e "═════════════════════════════════════════════${NC}"

# Step 1: Show current PM2 status
echo -e "${YELLOW}➤ Current PM2 status:${NC}"
pm2 status

# Step 2: Flush logs
echo -e "${YELLOW}➤ Flushing logs for ${SERVICE_NAME}...${NC}"
pm2 flush "$SERVICE_NAME" 2>/dev/null
echo -e "${GREEN}✔ Logs flushed.${NC}"

# Step 3: Delete PM2 process
echo -e "${YELLOW}➤ Stopping and deleting ${SERVICE_NAME} process...${NC}"
pm2 delete "$SERVICE_NAME" 2>/dev/null
echo -e "${GREEN}✔ PM2 process deleted.${NC}"

# Step 4: Save PM2 configuration
echo -e "${YELLOW}➤ Saving PM2 configuration...${NC}"
pm2 save
echo -e "${GREEN}✔ PM2 configuration saved.${NC}"

# Step 5: Show final status
echo -e "${YELLOW}➤ Final PM2 status:${NC}"
pm2 status

echo -e "${CYAN}🎯 ${SERVICE_NAME} stopped successfully.${NC}"
