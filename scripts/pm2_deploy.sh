#!/bin/bash

# Define colors for aesthetic output
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "🚀 Starting Python Task Aggregator with PM2"
echo -e "═════════════════════════════════════════════${NC}"

# Load .env variables
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo -e "${RED}✖ .env file not found. Deployment aborted.${NC}"
    exit 1
fi

# Validate SERVICE_NAME
if [ -z "$SERVICE_NAME" ]; then
    echo -e "${RED}✖ SERVICE_NAME not defined in .env. Deployment aborted.${NC}"
    exit 1
fi

# Step 1: Create logs directory
echo -e "${YELLOW}➤ Creating logs directory...${NC}"
mkdir -p logs
echo -e "${GREEN}✔ Logs directory ready.${NC}"

# Step 2: Install Python dependencies
echo -e "${YELLOW}➤ Checking Python dependencies...${NC}"
if [ -f "requirements.txt" ]; then
    if [ -d "venv" ]; then
        echo -e "${GRAY}Using virtual environment...${NC}"
        source venv/bin/activate
    fi

    pip install -r requirements.txt > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✔ Dependencies installed successfully.${NC}"
    else
        echo -e "${RED}✖ Failed to install dependencies. Deployment aborted.${NC}"
        exit 1
    fi
else
    echo -e "${RED}✖ requirements.txt not found. Deployment aborted.${NC}"
    exit 1
fi

# Step 3: Stop existing PM2 process
echo -e "${YELLOW}➤ Stopping existing PM2 process ($SERVICE_NAME)...${NC}"
pm2 delete "$SERVICE_NAME" 2>/dev/null
echo -e "${GREEN}✔ PM2 process deleted (if existed).${NC}"

# Step 4: Start application using PM2
echo -e "${YELLOW}➤ Starting Python application with PM2...${NC}"
if [ -f "ecosystem.config.js" ]; then
    pm2 start ecosystem.config.js
else
    echo -e "${RED}✖ ecosystem.config.js not found. Deployment aborted.${NC}"
    exit 1
fi

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✔ PM2 started successfully.${NC}"
else
    echo -e "${RED}✖ PM2 start failed. Deployment aborted.${NC}"
    exit 1
fi

# Step 5: Save PM2 process list
pm2 save > /dev/null

# Step 6: Show PM2 status
echo -e "${YELLOW}➤ PM2 Status:${NC}"
pm2 status

# Step 7: Stream logs
echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "📜 Tailing logs for $SERVICE_NAME"
echo -e "═════════════════════════════════════════════${NC}"
pm2 logs "$SERVICE_NAME"
