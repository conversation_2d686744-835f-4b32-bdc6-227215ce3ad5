#!/bin/bash

# Define colors
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Load .env and get SERVICE_NAME
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo -e "${RED}✖ .env file not found. Restart aborted.${NC}"
    exit 1
fi

# Validate SERVICE_NAME
if [ -z "$SERVICE_NAME" ]; then
    echo -e "${RED}✖ SERVICE_NAME not defined in .env. Restart aborted.${NC}"
    exit 1
fi

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "🔄 Restarting $SERVICE_NAME"
echo -e "═════════════════════════════════════════════${NC}"

# Check if process exists
if pm2 describe "$SERVICE_NAME" > /dev/null 2>&1; then
    echo -e "${YELLOW}➤ Restarting $SERVICE_NAME...${NC}"
    pm2 restart "$SERVICE_NAME"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✔ Service restarted successfully.${NC}"
    else
        echo -e "${RED}✖ Failed to restart service.${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}➤ Process not found. Starting from ecosystem.config.js...${NC}"
    
    if [ -f "ecosystem.config.js" ]; then
        pm2 start ecosystem.config.js
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✔ Service started successfully from config.${NC}"
        else
            echo -e "${RED}✖ Failed to start service from config.${NC}"
            exit 1
        fi
    else
        echo -e "${RED}✖ ecosystem.config.js not found. Cannot start service.${NC}"
        exit 1
    fi
fi

# Save PM2 configuration
pm2 save > /dev/null

# Show status
echo -e "\n${YELLOW}➤ Current PM2 Status:${NC}"
pm2 status

echo -e "\n${CYAN}🎯 Restart process for $SERVICE_NAME completed.${NC}"
