# fin_llm_splitter/rabbitmq/publisher.py

import json
import pika
from config.config import Config


def get_connection():
    return pika.BlockingConnection(
        pika.ConnectionParameters(
            host=Config.RABBITMQ_HOST,
            port=Config.RABBITMQ_PORT,
            credentials=pika.PlainCredentials(
                Config.RABBITMQ_USERNAME, Config.RABBITMQ_PASSWORD
            ),
        )
    )


def publish_batch_task(payload: dict):
    conn = get_connection()
    ch = conn.channel()
    ch.queue_declare(queue=Config.QUEUE_TASK_ROUTER, durable=True)
    ch.basic_publish(
        exchange="",
        routing_key=Config.QUEUE_TASK_ROUTER,
        body=json.dumps(payload),
        properties=pika.BasicProperties(delivery_mode=2),
    )
    conn.close()
