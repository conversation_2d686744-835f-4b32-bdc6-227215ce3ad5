# fin_llm_splitter/rabbitmq/consumer.py

import pika
import json
import traceback
from agent.splitter import get_splitter_graph
from rabbitmq.publisher import publish_batch_task
from config.config import Config
from utils.logger import logger

graph = get_splitter_graph()


def callback(ch, method, properties, body):
    try:
        data = json.loads(body)
        session_id = data.get("session_id")
        user_id = data.get("user_id")
        prompt = data.get("message")
        timestamp = data.get("timestamp")

        logger.info(f"📥 Message from {session_id}")

        result = graph.invoke(
            {
                "message": prompt,
                "user_id": user_id,
                "session_id": session_id,
                "timestamp": timestamp,
            }
        )

        batch = result["batch_payload"]
        publish_batch_task(batch)

        ch.basic_ack(delivery_tag=method.delivery_tag)

    except Exception:
        logger.error(traceback.format_exc())
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)


def start_consumer():
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(
            host=Config.RABBITMQ_HOST,
            port=Config.RABBITMQ_PORT,
            credentials=pika.PlainCredentials(
                Config.RABBITMQ_USERNAME, Config.RABBITMQ_PASSWORD
            ),
        )
    )
    channel = connection.channel()
    channel.queue_declare(queue=Config.QUEUE_CHAT_INPUT, durable=True)
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(queue=Config.QUEUE_CHAT_INPUT, on_message_callback=callback)

    logger.info("🚀 fin_llm_splitter running...")
    try:
        channel.start_consuming()
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted")
        channel.stop_consuming()
    finally:
        connection.close()
