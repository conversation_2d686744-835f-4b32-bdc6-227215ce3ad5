# fin_llm_splitter/agent/splitter.py

import json
import logging
from typing import TypedDict, List

from config.config import Config
from utils.logger import logger

from langchain_openai import ChatOpenAI
from langchain.prompts import (
    ChatPromptTemplate,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
)

from langgraph.graph import StateGraph, END

# ✅ Init LangChain LLM
llm = ChatOpenAI(
    model="gpt-4",
    temperature=0,
    api_key=Config.OPENAI_API_KEY,
)

# ✅ Prompt (escaped)
system_template = """
Kamu adalah AI yang memecah instruksi pengguna menjadi langkah-langkah yang dapat dijalankan sistem.

Setiap langkah wajib punya:
- step: mulai dari 1
- intent: query | summary | chart
- prompt: instruksi spesifik

Contoh:
[
  {{ "step": 1, "intent": "query", "prompt": "Ambil data penjualan minggu ini" }},
  {{ "step": 2, "intent": "summary", "prompt": "Ringkas hasil step 1" }},
  {{ "step": 3, "intent": "chart", "prompt": "Buat grafik mingguan dari step 1" }}
]
"""

prompt_template = ChatPromptTemplate.from_messages(
    [
        SystemMessagePromptTemplate.from_template(system_template.strip()),
        HumanMessagePromptTemplate.from_template("{user_prompt}"),
    ]
)


# ✅ LangGraph state
class SplitState(TypedDict):
    message: str
    user_id: str
    session_id: str
    original_message: str
    timestamp: str
    batch_payload: dict


def split_prompt_state(state: SplitState) -> SplitState:
    user_prompt = state["message"]
    user_id = state["user_id"]
    session_id = state["session_id"]
    timestamp = state["timestamp"]

    messages = prompt_template.format_messages(user_prompt=user_prompt)

    try:
        result = llm.invoke(messages)
        content = result.content.strip()
        logger.info(f"✅ LLM raw result: {content}")
        steps = json.loads(content)

        enriched = {
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": timestamp,
            "status": "task.split.success",
            "steps": steps,
        }

        return {
            "message": user_prompt,
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": timestamp,
            "batch_payload": enriched,
        }

    except Exception:
        logger.exception("❌ Gagal proses LLM, fallback ke summary")
        fallback = {
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": timestamp,
            "status": "task.split.fallback",
            "steps": [{"step": 1, "intent": "summary", "prompt": user_prompt}],
        }
        return {
            "message": user_prompt,
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": timestamp,
            "batch_payload": fallback,
        }


def get_splitter_graph():
    workflow = StateGraph(SplitState)
    workflow.add_node("splitter", split_prompt_state)
    workflow.set_entry_point("splitter")
    workflow.set_finish_point("splitter")
    return workflow.compile()
